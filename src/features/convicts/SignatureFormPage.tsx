import { useState, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'wouter';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { ArrowLeftIcon, ChevronLeftIcon, ChevronRightIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import { Link } from 'wouter';
import { getConvictWithDetails } from '../../lib/tauri-api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { formatDate } from '../../lib/utils';
import { generateSignatureDates, formatPeriodDisplay, groupSignaturePeriods } from '../../lib/signature-dates';
import type { SignaturePeriod } from '../../shared/schema';
import * as ExcelJS from 'exceljs';
import { save } from '@tauri-apps/plugin-dialog';
import { writeFile } from '@tauri-apps/plugin-fs';
import './SignatureForm.css';

export default function SignatureFormPage() {
  const { id } = useParams<{ id: string }>();
  const convictId = parseInt(id!, 10);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 24; // 24 signatures per page

  // PDF generation state
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  // Ref for the printable content
  const printContentRef = useRef<HTMLDivElement>(null);

  // Fetch convict details
  const { data: convict, isLoading, error } = useQuery({
    queryKey: ['convict-details', convictId],
    queryFn: () => getConvictWithDetails(convictId),
    enabled: !!convictId,
  });

  const handleExportExcel = async () => {
    if (!convict) {
      return;
    }

    try {
      setIsGeneratingPDF(true);

      // Create new workbook using ExcelJS for advanced formatting
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('İmza Föyü');

      // Define colors
      const headerBgColor = 'FFDCDDEA'; // Light gray background
      const borderStyle = { style: 'thin' as const, color: { argb: 'FF000000' } };

      // Set page setup for A4
      worksheet.pageSetup = {
        paperSize: 9, // A4
        orientation: 'portrait',
        margins: {
          left: 0.7, right: 0.7, top: 0.75, bottom: 0.75,
          header: 0.3, footer: 0.3
        }
      };

      // Title row (row 1) - "YÜKÜMLÜ İMZA ÇİZELGESİ"
      worksheet.mergeCells('A1:F1');
      const titleCell = worksheet.getCell('A1');
      titleCell.value = 'YÜKÜMLÜ İMZA ÇİZELGESİ';
      titleCell.font = { bold: true, size: 14 };
      titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

      // Header section starts at row 3
      let currentRow = 3;

      // Create the complex header layout
      // Row 3-8: Main header section with convict information
      const headerStartRow = currentRow;
      const headerEndRow = currentRow + 5; // 6 rows for header

      // Column A: "Yükümlünün" (rotated text)
      worksheet.mergeCells(`A${headerStartRow}:A${headerEndRow}`);
      const yukumluCell = worksheet.getCell(`A${headerStartRow}`);
      yukumluCell.value = 'Yükümlünün';
      yukumluCell.font = { bold: true, size: 12 };
      yukumluCell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
        textRotation: 90
      };
      yukumluCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: headerBgColor } };
      yukumluCell.border = {
        top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
      };

      // Column B: Field labels
      const fieldLabels = [
        'Dosya Numarası',
        'Adı Soyadı',
        'T.C. Kimlik Numarası',
        'Telefon Numarası',
        'Yakınına Ait Telefon Numarası',
        'İkamet Adresi'
      ];

      // Column C: Field values
      const fieldValues = [
        convict.file_number || '-',
        `${convict.first_name.toUpperCase()} ${convict.last_name.toUpperCase()}`,
        convict.tc_no || '-',
        convict.phone_number || '-',
        convict.relative_phone_number || '-',
        convict.address || '-'
      ];

      for (let i = 0; i < fieldLabels.length; i++) {
        const row = headerStartRow + i;

        // Field label (Column B)
        const labelCell = worksheet.getCell(`B${row}`);
        labelCell.value = fieldLabels[i];
        labelCell.font = { bold: true, size: 10 };
        labelCell.alignment = { horizontal: 'left', vertical: 'middle' };
        labelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: headerBgColor } };
        labelCell.border = {
          top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
        };

        // Field value (Column C)
        const valueCell = worksheet.getCell(`C${row}`);
        valueCell.value = fieldValues[i];
        valueCell.font = { bold: true, size: 8 };
        valueCell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        valueCell.border = {
          top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
        };
      }

      // Column D: "İmza Gün ve Saatleri" (rotated text)
      worksheet.mergeCells(`D${headerStartRow}:D${headerEndRow}`);
      const scheduleCell = worksheet.getCell(`D${headerStartRow}`);
      scheduleCell.value = 'İmza Gün ve Saatleri';
      scheduleCell.font = { bold: true, size: 12 };
      scheduleCell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
        textRotation: 90
      };
      scheduleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: headerBgColor } };
      scheduleCell.border = {
        top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
      };

      // Column E: Schedule information
      worksheet.mergeCells(`E${headerStartRow}:E${headerEndRow}`);
      const scheduleInfoCell = worksheet.getCell(`E${headerStartRow}`);
      let scheduleText = '';
      signaturePeriods.forEach((period, index) => {
        if (index > 0) scheduleText += '\n\n';
        scheduleText += `${period.startDate} - ${period.endDate}\n`;
        scheduleText += `${period.frequency}\n`;
        if (period.days) scheduleText += `${period.days}\n`;
        scheduleText += `${period.time}`;
      });
      scheduleInfoCell.value = scheduleText;
      scheduleInfoCell.font = { size: 8 };
      scheduleInfoCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      scheduleInfoCell.border = {
        top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
      };

      // Column F: Signature sample section - split into two parts
      // Top part: Sequence number box (smaller area)
      const sequenceBoxEndRow = headerStartRow + 1; // Only 2 rows for sequence number
      worksheet.mergeCells(`F${headerStartRow}:F${sequenceBoxEndRow}`);
      const sequenceCell = worksheet.getCell(`F${headerStartRow}`);
      sequenceCell.value = convict.sequence_number || '';
      sequenceCell.font = { size: 24, bold: true }; // Slightly smaller font
      sequenceCell.alignment = { horizontal: 'center', vertical: 'middle' };
      sequenceCell.border = {
        top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
      };

      // Bottom part: Label text (larger area)
      const labelStartRow = sequenceBoxEndRow + 1;
      worksheet.mergeCells(`F${labelStartRow}:F${headerEndRow}`);
      const labelCell = worksheet.getCell(`F${labelStartRow}`);
      labelCell.value = 'YÜKÜMLÜNÜN\nİMZA ÖRNEĞİ';
      labelCell.font = { size: 10 };
      labelCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      labelCell.border = {
        top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
      };

      currentRow = headerEndRow + 2; // Skip a row

      // Table section headers
      const tableSectionRow = currentRow;

      // Section labels row
      worksheet.mergeCells(`A${tableSectionRow}:C${tableSectionRow}`);
      const convictSectionCell = worksheet.getCell(`A${tableSectionRow}`);
      convictSectionCell.value = 'YÜKÜMLÜNÜN';
      convictSectionCell.font = { size: 12 };
      convictSectionCell.alignment = { horizontal: 'center', vertical: 'middle' };
      convictSectionCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: headerBgColor } };
      convictSectionCell.border = {
        top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
      };

      worksheet.mergeCells(`D${tableSectionRow}:F${tableSectionRow}`);
      const officerSectionCell = worksheet.getCell(`D${tableSectionRow}`);
      officerSectionCell.value = 'GÖREVLİNİN';
      officerSectionCell.font = { size: 12 };
      officerSectionCell.alignment = { horizontal: 'center', vertical: 'middle' };
      officerSectionCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: headerBgColor } };
      officerSectionCell.border = {
        top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
      };

      currentRow++;

      // Column headers row
      const columnHeaders = ['S.N.', 'İMZA TARİHİ', 'İMZA SAATİ', 'İMZASI', 'ADI SOYADI/ÜNVANI', 'İMZASI'];
      for (let i = 0; i < columnHeaders.length; i++) {
        const headerCell = worksheet.getCell(currentRow, i + 1);
        headerCell.value = columnHeaders[i];
        headerCell.font = { bold: true, size: 10 };
        headerCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        headerCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: headerBgColor } };
        headerCell.border = {
          top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
        };
      }

      currentRow++;

      // Data rows
      allSignatureDates.forEach((dateStr, index) => {
        const dataRow = currentRow + index;

        // Set row height for data rows
        worksheet.getRow(dataRow).height = 18; // Compact height for data rows

        // S.N.
        const snCell = worksheet.getCell(dataRow, 1);
        snCell.value = index + 1;
        snCell.font = { size: 10 }; // Slightly smaller font
        snCell.alignment = { horizontal: 'center', vertical: 'middle' };
        snCell.border = {
          top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
        };

        // Date
        const dateCell = worksheet.getCell(dataRow, 2);
        dateCell.value = dateStr;
        dateCell.font = { size: 8 };
        dateCell.alignment = { horizontal: 'left', vertical: 'middle' };
        dateCell.border = {
          top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
        };

        // Empty cells for signatures
        for (let col = 3; col <= 6; col++) {
          const emptyCell = worksheet.getCell(dataRow, col);
          emptyCell.border = {
            top: borderStyle, bottom: borderStyle, left: borderStyle, right: borderStyle
          };
        }
      });

      // Set column widths to match the form layout
      worksheet.getColumn(1).width = 5;   // S.N.
      worksheet.getColumn(2).width = 15;  // Date
      worksheet.getColumn(3).width = 12;  // Time
      worksheet.getColumn(4).width = 15;  // Convict signature
      worksheet.getColumn(5).width = 20;  // Officer name
      worksheet.getColumn(6).width = 15;  // Officer signature

      // Set row heights - more compact and fitting
      for (let i = headerStartRow; i <= headerEndRow; i++) {
        worksheet.getRow(i).height = 25; // Reduced from 40 to 25
      }
      worksheet.getRow(tableSectionRow).height = 20; // Slightly reduced
      worksheet.getRow(tableSectionRow + 1).height = 20; // Slightly reduced

      // Generate Excel file
      const buffer = await workbook.xlsx.writeBuffer();

      // Save file
      const defaultName = `İmza_Föyü_${convict.first_name}_${convict.last_name}_${formatDate(new Date()).replace(/\./g,'_')}.xlsx`;
      const filePath = await save({
        filters: [{ name: 'Excel Dosyaları', extensions: ['xlsx'] }],
        defaultPath: defaultName
      });

      if (filePath) {
        await writeFile(filePath, new Uint8Array(buffer));
        console.log('Excel başarıyla kaydedildi:', filePath);
      }
    } catch (error) {
      console.error('Excel oluşturma hatası:', error);
      alert('Excel dosyası oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  if (isLoading) {
    return (
      <div className="windows-content">
        <div className="flex flex-col items-center justify-center py-8">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Hükümlü bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error || !convict) {
    return (
      <div className="windows-content">
        <Card className="windows-card">
          <CardContent className="text-center py-8">
            <p className="text-red-600">Hükümlü bilgileri yüklenirken bir hata oluştu.</p>
            <Link href="/convicts">
              <Button variant="windows" className="mt-4">
                <ArrowLeftIcon className="w-4 h-4 mr-2" />
                Hükümlü Listesine Dön
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Group signature periods with same date range, type, and time
  const groupedPeriods = groupSignaturePeriods(convict.activePeriods || []);

  // Process signature periods for display using the new utility
  const signaturePeriods = groupedPeriods.map((period: SignaturePeriod) => {
    const { frequency, days, time } = formatPeriodDisplay(period);
    // Use actual time range from database if available
    const actualTime = (period.time_start && period.time_end) 
      ? `Saat ${period.time_start} - ${period.time_end}`
      : time;
    
    return {
      startDate: formatDate(period.start_date),
      endDate: formatDate(period.end_date),
      frequency,
      days,
      time: actualTime
    };
  }) || [];

  // Generate signature dates using the new utility
  // Calculate appropriate number of dates based on periods
  const activePeriods = convict.activePeriods || [];
  
  // Calculate total period duration to determine number of pages needed
  let totalDatesNeeded = 24; // Default minimum
  
  if (activePeriods.length > 0) {
    // Estimate dates needed based on frequency
    let estimatedSignatureDays = 0;
    activePeriods.forEach(period => {
      const periodDays = Math.ceil((new Date(period.end_date).getTime() - new Date(period.start_date).getTime()) / (1000 * 60 * 60 * 24));
      
      if (period.frequency_type === 'WEEKLY') {
        const weeklyDays = period.frequency_value.split(',').length;
        estimatedSignatureDays += Math.ceil(periodDays / 7) * weeklyDays;
      } else if (period.frequency_type === 'X_DAYS') {
        const interval = parseInt(period.frequency_value) || 1;
        estimatedSignatureDays += Math.ceil(periodDays / interval);
      } else if (period.frequency_type === 'MONTHLY_SPECIFIC') {
        const monthlyDays = period.frequency_value.split(',').length;
        const months = Math.ceil(periodDays / 30);
        estimatedSignatureDays += months * monthlyDays;
      }
    });
    
    // Use estimated days but ensure minimum of 24 and maximum reasonable limit
    totalDatesNeeded = Math.max(24, Math.min(estimatedSignatureDays, 200));
  }
  
  const generatedDates = generateSignatureDates(activePeriods, totalDatesNeeded, false); // Start from period start, not today
  const allSignatureDates = generatedDates.map(d => `${d.date} (${d.dayOfWeek})`);
  
  // Calculate pagination
  const totalPages = Math.ceil(allSignatureDates.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const signatureDates = allSignatureDates.slice(startIndex, endIndex);
  
  // Calculate date range for current page
  const pageStartDate = signatureDates.length > 0 ? signatureDates[0] : '';
  const pageEndDate = signatureDates.length > 0 ? signatureDates[signatureDates.length - 1] : '';

  // Calculate summary statistics (for future use)
  // const periodSummary = convict.activePeriods?.reduce((summary, period) => {
  //   const type = period.frequency_type;
  //   summary[type] = (summary[type] || 0) + 1;
  //   return summary;
  // }, {} as Record<string, number>) || {};

  return (
    <>
      {/* Print/Navigation Controls - Hidden in print */}
      <div className="windows-content print:hidden">
        <div className="windows-toolbar-secondary">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="windows-title">
                İmza Föyü
              </h1>
              <p className="windows-subtitle">
                {convict.first_name} {convict.last_name} - TC: {convict.tc_no}
                {totalPages > 1 && (
                  <span className="ml-4">
                    (Sayfa {currentPage}/{totalPages} - {pageStartDate} / {pageEndDate})
                  </span>
                )}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {/* Pagination Controls - Only show if more than 1 page */}
              {totalPages > 1 && (
                <div className="flex items-center space-x-1 mr-4">
                  <Button
                    variant="windows"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeftIcon className="w-4 h-4" />
                  </Button>
                  <span className="text-xs px-2 py-1 bg-gray-100 rounded">
                    {currentPage}/{totalPages}
                  </span>
                  <Button
                    variant="windows"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRightIcon className="w-4 h-4" />
                  </Button>
                </div>
              )}
              
              <Button
                variant="windows-primary"
                size="sm"
                onClick={handleExportExcel}
                disabled={isGeneratingPDF}
              >
                <DocumentArrowDownIcon className="w-4 h-4 mr-2" />
                {isGeneratingPDF ? 'Excel Oluşturuluyor...' : 'Excel Olarak Kaydet'}
              </Button>
              <Link href="/convicts">
                <Button variant="windows" size="sm">
                  <ArrowLeftIcon className="w-4 h-4 mr-2" />
                  Listeye Dön
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Signature Form Document - Styled for A4 printing */}
      <div
        ref={printContentRef}
        className="signature-form-document print:p-0 print:m-0 print:shadow-none print:border-none p-8"
      >
        <div className="signature-form-page">
          {/* Document Title - Positioned at bottom center */}
          <div className="signature-form-title">
            YÜKÜMLÜ İMZA ÇİZELGESİ
          </div>
          {/* Header Section */}
          <div className="signature-form-header">
            {/* Left Section - Labels */}
            <div className="signature-form-header-section signature-form-header-label">
              <div className="signature-form-header-title">
                Yükümlünün
              </div>
            </div>

            {/* Middle Section - Field Labels */}
            <div className="signature-form-header-section signature-form-header-fields">
              <div className="signature-form-field">
                <div className="signature-form-field-label">Dosya Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">Adı Soyadı</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">T.C. Kimlik Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">Telefon Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">Yakınına Ait Telefon Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">İkamet Adresi</div>
              </div>
            </div>

            {/* Right Section - Field Values */}
            <div className="signature-form-header-section signature-form-header-values">
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.file_number || '-'}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.first_name.toUpperCase()} {convict.last_name.toUpperCase()}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.tc_no}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.phone_number || '-'}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.relative_phone_number || '-'}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">
                  {convict.address ? (
                    convict.address.split('\n').map((line, index) => (
                      <span key={index}>
                        {line}
                        {index < convict.address!.split('\n').length - 1 && <br />}
                      </span>
                    ))
                  ) : (
                    '-'
                  )}
                </div>
              </div>
            </div>

            {/* Signature Schedule Section */}
            <div className="signature-form-header-section signature-form-schedule-label">
              <div className="signature-form-header-title">
                İmza Gün ve Saatleri
              </div>
            </div>

            <div className="signature-form-header-section signature-form-schedule-values">
              <div className="signature-form-schedule">
                {signaturePeriods.map((period, index) => (
                  <div key={index} className="signature-form-schedule-item">
                    <div className="signature-form-schedule-text">
                      {period.startDate} - {period.endDate}
                    </div>
                    <div className="signature-form-schedule-text">
                      {period.frequency}
                    </div>
                    {period.days && (
                      <div className="signature-form-schedule-text">
                        {period.days}
                      </div>
                    )}
                    <div className="signature-form-schedule-text">
                      {period.time}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Signature Sample Section */}
            <div className="signature-form-header-section signature-form-sample">
              <div className="signature-form-sample-box">
                <div className="signature-form-sample-number">{convict.sequence_number || ' '}</div>
              </div>
              <div className="signature-form-sample-label">
                YÜKÜMLÜNÜN
                <br />
                İMZA ÖRNEĞİ
              </div>
            </div>
          </div>

          {/* Table Section */}
          <div className="signature-form-table">
            {/* Table Headers */}
            <div className="signature-form-table-headers">
              <div className="signature-form-table-section-labels">
                <div className="signature-form-table-section-label signature-form-table-section-convict">YÜKÜMLÜNÜN</div>
                <div className="signature-form-table-section-label signature-form-table-section-officer">GÖREVLİNİN</div>
              </div>
              <div className="signature-form-table-header-section">
                <div className="signature-form-table-header signature-form-table-header-sn">S.N.</div>
                <div className="signature-form-table-header signature-form-table-header-date">İMZA TARİHİ</div>
                <div className="signature-form-table-header signature-form-table-header-time">İMZA SAATİ</div>
                <div className="signature-form-table-header signature-form-table-header-convict-sig">İMZASI</div>
                <div className="signature-form-table-header signature-form-table-header-officer-name">ADI SOYADI/ÜNVANI</div>
                <div className="signature-form-table-header signature-form-table-header-officer-sig">İMZASI</div>
              </div>
            </div>

            {/* Table Rows */}
            <div className="signature-form-table-rows">
              {signatureDates.map((date, index) => (
                <div key={index} className="signature-form-table-row">
                  <div className="signature-form-table-cell signature-form-table-cell-sn">
                    {index + 1}
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-date">
                    {date}
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-time">
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-convict-sig">
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-officer-name">
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-officer-sig">
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
